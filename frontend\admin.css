@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
    --primary-color: #00C2A8;
    --primary-dark: #00a58e;
    --sidebar-bg: #1e293b;
    --sidebar-text: #e2e8f0;
    --sidebar-hover: #334155;
    --bg-color: #f1f5f9;
    --card-bg: #ffffff;
    --text-color: #1e293b;
    --text-muted: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--bg-color);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Layout */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    padding: 1.5rem 0;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.sidebar-header {
    padding: 0 1.5rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1rem;
}

.sidebar-header h2 {
    color: white;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.sidebar-nav {
    flex: 1;
    overflow-y: auto;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: all 0.2s;
    font-weight: 500;
    font-size: 0.9375rem;
}

.nav-item i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.nav-item:hover {
    background-color: var(--sidebar-hover);
    color: white;
}

.nav-item.active {
    background-color: rgba(0, 194, 168, 0.2);
    color: white;
    border-left: 3px solid var(--primary-color);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: calc(100% - 250px);
    width: 100%;
    overflow: hidden;
}

/* Top Bar */
.top-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    background-color: var(--card-bg);
    box-shadow: var(--shadow-sm);
    z-index: 10;
}

.search-bar {
    position: relative;
    width: 300px;
}

.search-bar input {
    width: 100%;
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.9375rem;
    transition: all 0.2s;
}

.search-bar input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 194, 168, 0.1);
}

.search-bar i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-name {
    font-weight: 500;
    font-size: 0.9375rem;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

/* Content Wrapper */
.content-wrapper {
    flex: 1;
    padding: 1.5rem 2rem;
    overflow-y: auto;
    position: relative;
}

/* Tabs */
.tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
    gap: 0.5rem;
}

.tab-btn {
    padding: 0.75rem 1.25rem;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    color: var(--text-muted);
    font-weight: 500;
    font-size: 0.9375rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s;
}

.tab-btn i {
    font-size: 1rem;
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* Tab Content */
.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Cards */
.card {
    background: var(--card-bg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-color);
}

.card-body {
    padding: 1.5rem;
}

/* Summary Card */
.summary-card {
    grid-column: 1 / -1;
}

.summary-text {
    line-height: 1.7;
    color: var(--text-color);
    white-space: pre-line;
}

/* Query Card */
.query-card {
    grid-column: 1 / -1;
}

.query-tool {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.query-input-row {
    display: flex;
    gap: 0.75rem;
}

.query-input-row input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.9375rem;
    transition: all 0.2s;
}

.query-input-row input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 194, 168, 0.1);
}

.suggestion-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.btn-pill {
    background-color: #f1f5f9;
    border: 1px solid #e2e8f0;
    border-radius: 9999px;
    padding: 0.375rem 0.875rem;
    font-size: 0.8125rem;
    font-weight: 500;
    color: var(--text-muted);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    transition: all 0.2s;
}

.btn-pill:hover {
    background-color: #e2e8f0;
    color: var(--text-color);
}

.btn-pill i {
    font-size: 0.75rem;
}

/* Result Block */
#queryResult {
    margin-top: 1rem;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.result-header label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-muted);
}

.result-block {
    background-color: #f8fafc;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 1rem;
    min-height: 120px;
    max-height: 300px;
    overflow-y: auto;
    font-size: 0.9375rem;
    line-height: 1.6;
}

/* Chart Cards */
.chart-card canvas {
    width: 100% !important;
    height: 300px !important;
}

/* Generate Data Panel */
.generate-data-panel {
    background: var(--card-bg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-top: 2rem;
    overflow: hidden;
}

.panel-header {
    padding: 1rem 1.5rem;
    background-color: #f8fafc;
    border-bottom: 1px solid var(--border-color);
}

.panel-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.panel-header i {
    color: var(--primary-color);
}

.panel-body {
    padding: 1.5rem;
}

.form-row {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
}

.form-group {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-muted);
}

.form-control {
    width: 100%;
    padding: 0.625rem 0.875rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.9375rem;
    transition: all 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 194, 168, 0.1);
}

.count-input {
    max-width: 100px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: 0.9375rem;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
}

.btn i {
    font-size: 0.875rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.2s;
}

.btn-icon:hover {
    background-color: #f1f5f9;
    color: var(--primary-color);
}

/* Status Messages */
.status-message {
    margin-top: 1rem;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    display: none;
}

.status-message.success {
    background-color: #ecfdf5;
    color: #065f46;
    border: 1px solid #a7f3d0;
    display: block;
}

.status-message.error {
    background-color: #fef2f2;
    color: #b91c1c;
    border: 1px solid #fca5a5;
    display: block;
}

.status-message.info {
    background-color: #eff6ff;
    color: #1e40af;
    border: 1px solid #93c5fd;
    display: block;
}

/* Animations */
@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading {
    position: relative;
    color: transparent;
}

.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-color);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

/* Responsive */
@media (max-width: 1024px) {
    .sidebar {
        width: 220px;
    }
    
    .main-content {
        max-width: calc(100% - 220px);
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 60px;
        overflow: hidden;
    }
    
    .sidebar-header h2,
    .nav-item span {
        display: none;
    }
    
    .nav-item {
        justify-content: center;
        padding: 1rem 0;
    }
    
    .nav-item i {
        margin-right: 0;
        font-size: 1.25rem;
    }
    
    .main-content {
        max-width: calc(100% - 60px);
    }
    
    .top-bar {
        padding: 0.75rem 1rem;
    }
    
    .search-bar {
        width: 200px;
    }
    
    .content-wrapper {
        padding: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .count-input {
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    .search-bar {
        display: none;
    }
    
    .top-bar {
        justify-content: flex-end;
    }
    
    .tabs {
        overflow-x: auto;
        padding-bottom: 0.5rem;
    }
    
    .tab-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
}

.container {
    max-width: 1200px;
    width: 98%;
    margin: 32px auto 32px auto;
    padding: 0 0 32px 0;
    background: transparent;
    box-sizing: border-box;
}

@media (max-width: 700px) {
    .container {
        max-width: 100vw;
        width: 100%;
        padding: 6px;
    }
    .dashboard-section {
        padding: 8px 0 0 0;
    }
    .summary-block, .result-block {
        font-size: 0.97rem;
        padding: 8px;
    }
    #issuesChart, #sentimentChart {
        padding: 3px;
    }
    .query-input-row {
        flex-direction: column;
        gap: 6px;
    }
    .suggestion-buttons {
        gap: 6px;
    }
    .top-row {
        flex-direction: column !important;
    }
    #summarySection, #querySection {
        width: 100% !important;
        min-width: 0 !important;
        margin-right: 0 !important;
    }
}

.dashboard-header {
    text-align: center;
    margin: 0 auto 38px auto;
    max-width: 700px;
    padding: 40px 0 0 0;
}
.dashboard-title {
    font-size: 2.3rem;
    font-weight: 700;
    letter-spacing: -0.02em;
    color: #23272f;
    margin-bottom: 0.2em;
}
.dashboard-subtitle {
    color: #6b7280;
    font-size: 1.12rem;
    font-weight: 400;
    margin-bottom: 0;
}


.dashboard-card {
    background: #fff;
    box-shadow: 0 2px 16px 0 rgba(60,72,88,0.08);
    border-radius: 14px;
    padding: 32px 28px 28px 28px;
    border: 1px solid #ececec;
    min-width: 0;
    width: 100%;
    min-height: 340px;
    display: flex;
    flex-direction: column;
    gap: 18px;
    transition: box-shadow 0.18s;
    position: relative;
    animation: fadein 0.7s;
    margin: 0;
    justify-content: flex-start;
    box-sizing: border-box;
    overflow: hidden;
}
@media (max-width: 900px) {
    .dashboard-card {
        padding: 22px 10px 22px 10px;
        min-height: 260px;
    }
}
.card-title.center {
    text-align: center;
}

.card-title {
    font-size: 1.18rem;
    font-weight: 600;
    color: #23272f;
    margin-bottom: 10px;
    letter-spacing: 0.01em;
}
@media (max-width: 700px) {
    .dashboard-card {
        padding: 18px 8px 18px 8px;
        margin-bottom: 18px;
    }
}


.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    column-gap: 40px;
    row-gap: 40px;
    margin: 0 auto 40px auto;
    max-width: 1100px;
    box-sizing: border-box;
}
@media (max-width: 900px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-rows: none;
        column-gap: 0;
        row-gap: 24px;
        max-width: 98vw;
    }
}


.charts-row {
    display: flex;
    flex-direction: row;
    gap: 32px;
    justify-content: center;
    margin: 0 auto 32px auto;
    max-width: 900px;
}
@media (max-width: 900px) {
    .charts-row {
        flex-direction: column;
        gap: 18px;
        max-width: 100vw;
    }
}


@media (max-width: 900px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }
}
#summarySection, #querySection {
    min-width: 0;
    margin-right: 0;
}
.summary-block, .result-block {
    background: #f7f8fa;
    padding: 20px 18px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    min-height: 110px;
    max-height: 220px;
    font-size: 1.08rem;
    color: #23272f;
    margin-bottom: 10px;
    overflow-y: auto;
    box-sizing: border-box;
    width: 100%;
    resize: none;
    font-family: inherit;
    line-height: 1.6;
    box-shadow: 0 1px 4px 0 rgba(60,72,88,0.03);
    transition: all 0.2s ease;
}

/* Summary text states */
.summary-text {
    transition: all 0.2s ease;
}

.summary-text.loading {
    color: #4b5563;
    font-style: italic;
    position: relative;
}

.summary-text.loading::after {
    content: '...';
    display: inline-block;
    width: 1em;
    overflow: hidden;
    vertical-align: bottom;
    animation: ellipsis 1.5s infinite steps(4, end);
}

@keyframes ellipsis {
    0% { width: 0.2em; }
    50% { width: 1em; }
    100% { width: 0.2em; }
}

.summary-text.error {
    color: #dc2626;
    background-color: rgba(220, 38, 38, 0.05);
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #dc2626;
}
.summary-block:focus, .result-block:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}


h2 {
    color: #333;
    margin-bottom: 12px;
    font-size: 1.25rem;
    letter-spacing: 0.02em;
}


.summary-box-wrapper {
    margin-bottom: 10px;
}

.summary-box {
    width: 100%;
    min-height: 90px;
    max-height: 180px;
    resize: vertical;
    font-size: 1rem;
    padding: 14px;
    border-radius: 6px;
    border: 1px solid #ddd;
    background: #fff;
    color: #222;
    box-sizing: border-box;
}

.summary-controls {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 0;
}

.btn {
    font-family: inherit;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 5px;
    padding: 9px 20px;
    border: none;
    cursor: pointer;
    transition: background 0.18s, color 0.18s, box-shadow 0.18s;
    outline: none;
    box-shadow: 0 1px 2px 0 rgba(60,72,88,0.03);
}
.btn-primary {
    background: #2563eb;
    color: #fff;
    border: 1px solid #2563eb;
}
.btn-primary:hover, .btn-primary:focus {
    background: #1743a2;
    color: #fff;
    box-shadow: 0 2px 8px 0 rgba(37,99,235,0.10);
}
.btn-secondary {
    background: #f7f8fa;
    color: #23272f;
    border: 1px solid #cbd5e1;
}

/* Sample Data Section */
.sample-data-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-control {
    padding: 10px 14px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.status-message {
    margin: 8px 0 0 0;
    min-height: 24px;
    font-size: 0.95rem;
    line-height: 1.4;
}

.status-message.success {
    color: #059669;
}

.status-message.error {
    color: #dc2626;
}

#generateDataBtn {
    align-self: flex-start;
    margin-top: 8px;
}

#generateDataBtn:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sample-data-form {
        gap: 14px;
    }
    
    .form-control {
        padding: 8px 12px;
    }
}
.btn-secondary:hover, .btn-secondary:focus {
    background: #e5e7eb;
    color: #23272f;
}


.query-tool {
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.query-input-row {
    display: flex;
    gap: 10px;
    margin-bottom: 8px;
}
#queryInput {
    flex: 1;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ccc;
}
#submitQuery {
    padding: 10px 18px;
    border-radius: 4px;
    background: #2563eb;
    color: #fff;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}
#submitQuery:hover {
    background: #1743a2;
}
.suggestion-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 8px;
}
.btn-pill {
    padding: 7px 18px;
    border-radius: 999px;
    background: #f3f6fa;
    border: 1.5px solid #e5e7eb;
    color: #2563eb;
    font-size: 0.97rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.18s, color 0.18s, border 0.18s;
}
.btn-pill:hover, .btn-pill:focus {
    background: #e7edfa;
    color: #1743a2;
    border-color: #2563eb;
}

.result-box {
    width: 100%;
    min-height: 70px;
    max-height: 150px;
    resize: vertical;
    font-size: 1rem;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #ddd;
    background: #fff;
    color: #222;
    box-sizing: border-box;
}


#issuesSection, #sentimentSection {
    margin-bottom: 32px;
}

#issuesChart, #sentimentChart {
    display: block;
    width: 100%;
    max-width: 100%;
    min-width: 0;
    background: #fff;
    padding: 0;
    margin: 0 auto 8px auto;
    border-radius: 8px;
    box-shadow: none;
    border: none;
    min-height: 160px;
    box-sizing: border-box;
    overflow: auto;
}

.error-message {
    color: #dc3545;
    text-align: center;
    margin-top: 10px;
    font-size: 0.99rem;
}

.fade-in {
    animation: fadein 0.7s;
}
@keyframes fadein {
    from { opacity: 0; transform: translateY(18px); }
    to   { opacity: 1; transform: none; }
}


#suggestedQuestions {
    margin-top: 20px;
}

.suggestion-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.suggestion {
    background-color: #f0f0f0;
    color: #333;
    border: 1px solid #ddd;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9em;
    transition: background-color 0.3s, color 0.3s;
    cursor: pointer;
}

.suggestion:hover {
    background-color: #e0e0e0;
}

.suggestion:disabled {
    background-color: #f0f0f0;
    color: #999;
    cursor: not-allowed;
}
